<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI客服设置优化功能演示</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }
        
        .demo-header h1 {
            margin: 0 0 10px 0;
            font-size: 32px;
            font-weight: 700;
        }
        
        .demo-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 18px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .feature-icon {
            font-size: 48px;
            color: #4CAF50;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .feature-title {
            color: white;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .feature-desc {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            line-height: 1.6;
            text-align: center;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .section-title {
            color: white;
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .step-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .step-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.9);
            border-left: 4px solid #4CAF50;
        }
        
        .step-number {
            background: #4CAF50;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            margin-right: 10px;
        }
        
        .btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn:hover {
            background: #1976D2;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .btn-success {
            background: #4CAF50;
        }
        
        .btn-success:hover {
            background: #45a049;
        }
        
        .btn-warning {
            background: #ff9800;
        }
        
        .btn-warning:hover {
            background: #f57c00;
        }
        
        .actions {
            text-align: center;
            margin-top: 30px;
        }
        
        .actions .btn {
            margin: 0 10px;
        }
        
        .highlight {
            background: rgba(255, 193, 7, 0.2);
            color: #FFC107;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1><i class="fas fa-magic"></i> AI客服设置优化功能演示</h1>
            <p>全新的保存信息显示和管理功能，提升用户体验</p>
        </div>
        
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="feature-title">保存信息显示</div>
                <div class="feature-desc">
                    保存设置后自动显示详细的配置信息，包括时间、状态、密钥数量等
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="feature-title">管理功能</div>
                <div class="feature-desc">
                    一键打开API密钥管理弹窗，方便查看、验证和删除密钥
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-edit"></i>
                </div>
                <div class="feature-title">编辑功能</div>
                <div class="feature-desc">
                    快速切换回编辑模式，重新配置各项设置参数
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-trash"></i>
                </div>
                <div class="feature-title">删除功能</div>
                <div class="feature-desc">
                    安全地重置所有设置到默认状态，支持确认操作
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="section-title">
                <i class="fas fa-play-circle"></i>
                使用流程演示
            </div>
            
            <ol class="step-list">
                <li class="step-item">
                    <span class="step-number">1</span>
                    在DeepSeek或豆包设置页面中配置API密钥、模型等参数
                </li>
                <li class="step-item">
                    <span class="step-number">2</span>
                    点击<span class="highlight">"保存设置"</span>按钮保存配置
                </li>
                <li class="step-item">
                    <span class="step-number">3</span>
                    页面底部自动显示设置信息卡片，包含详细的配置信息
                </li>
                <li class="step-item">
                    <span class="step-number">4</span>
                    使用<span class="highlight">"管理"</span>按钮打开API密钥管理弹窗
                </li>
                <li class="step-item">
                    <span class="step-number">5</span>
                    使用<span class="highlight">"编辑"</span>按钮回到编辑模式
                </li>
                <li class="step-item">
                    <span class="step-number">6</span>
                    使用<span class="highlight">"删除"</span>按钮重置所有设置（需确认）
                </li>
            </ol>
        </div>
        
        <div class="demo-section">
            <div class="section-title">
                <i class="fas fa-check-double"></i>
                功能特点
            </div>
            
            <div style="color: rgba(255, 255, 255, 0.8); line-height: 1.8;">
                <p>✅ <strong>信息透明：</strong>清晰显示保存时间、功能状态、API密钥数量等详细信息</p>
                <p>✅ <strong>操作便捷：</strong>一键访问管理、编辑、删除功能，提升操作效率</p>
                <p>✅ <strong>视觉美观：</strong>现代化卡片设计，平滑动画效果，提升用户体验</p>
                <p>✅ <strong>功能一致：</strong>DeepSeek和豆包功能完全一致，统一的交互方式</p>
                <p>✅ <strong>安全可靠：</strong>删除操作需要确认，防止误操作导致数据丢失</p>
            </div>
        </div>
        
        <div class="actions">
            <a href="?page=ai_service_settings" class="btn btn-success">
                <i class="fas fa-rocket"></i> 立即体验
            </a>
            <a href="ai_service_test.html" class="btn btn-warning">
                <i class="fas fa-flask"></i> 功能测试
            </a>
            <a href="index.php" class="btn">
                <i class="fas fa-home"></i> 返回首页
            </a>
        </div>
    </div>
</body>
</html>
